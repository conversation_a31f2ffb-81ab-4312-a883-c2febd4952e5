use std::collections::HashMap;
use std::error::Error;
use std::fmt::Debug;
use std::path::Path;
use std::str::FromStr;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use futures::TryFutureExt;
use lazy_static::lazy_static;
use log::{error, info};
use once_cell::sync::Lazy;
use serde_json::{json, Value};
use sha1::{Digest, Sha1};
use similar::DiffableStr;
use tokio::sync::Mutex;
use tokio::time::{sleep, timeout};
use tokio::time::error::Elapsed;
use tracing_subscriber::fmt::time::FormatTime;
use crate::remote::merkle_tree;
use crate::remote::merkle_tree::build_merkle_tree;
use crate::remote::merkle_tree_diff::{compare_merkle_tree, DiffResult};
use crate::remote::remote_syn_data::{DiffFileData, DiffFileDataWrapper, HealthRequestBean, LocalDiffDataForSearchFromRemote, RemoteIndexConfig, RepoGitInfo, RequestMerkleTreeData, SearchFromRemoteRequestData, UploadDiffFileData};
use crate::remote::remote_syn_net::{request_merkle_tree_config, request_remote_merkle_tree, request_search_from_remote, request_upload_diff_file};
use crate::remote::remote_util::{generate_session_id, get_diff_file_vec, get_remote_index_config};
use crate::remote::timeout_fun::run_task_with_timeout;
use crate::utils::file_encoding::read_file_smart_sync;

//隔多久进行一次仓库的遍历
pub const TICK_INTERVAL_SECS_TIME: usize = 10*60;    //10 minutes

lazy_static! {
   pub static ref REMOTE_DATA_SYN_MANAGER: RemoteDataSyncManager = RemoteDataSyncManager::new();
}

static INIT_TICK_TASK: Lazy<Arc<AtomicBool>> = Lazy::new(|| Arc::new(AtomicBool::new(false)));

pub struct RemoteDataSyncManager {
    //userId
    pub userId: Mutex<Option<String>>,
    //userToken
    pub userToken: Mutex<Option<String>>,
    //插件版本
    pub pluginVersion: Mutex<Option<String>>,
    //用户使用的产品
    pub productType: Mutex<Option<String>>,
    //ide版本
    pub ideVersion: Mutex<Option<String>>,
    //agent版本
    pub agentVersion: Mutex<Option<String>>,
    //仓库信息列表
    pub repoGitInfoContainer: Mutex<HashMap<String, RepoGitInfo>>
}

// 显式实现 Send 和 Sync
unsafe impl Send for RemoteDataSyncManager {}
unsafe impl Sync for RemoteDataSyncManager {}

impl RemoteDataSyncManager {
    pub fn new() -> Self {
        RemoteDataSyncManager {
            userId: Mutex::new(None),
            userToken: Mutex::new(None),
            pluginVersion: Mutex::new(None),
            productType: Mutex::new(None),
            ideVersion: Mutex::new(None),
            agentVersion: Mutex::new(None),
            repoGitInfoContainer: Mutex::new(HashMap::new()),
        }
    }

    //更新成最新的值
    pub async fn refresh_data (&self, health_request_bean: Option<HealthRequestBean>) {
        if health_request_bean.is_none() {
            return;
        }
        let health_request_bean_clone = health_request_bean.unwrap().clone();

        // 更新各个字段
        *self.userId.lock().await = health_request_bean_clone.userId;
        *self.userToken.lock().await = health_request_bean_clone.userToken;
        *self.pluginVersion.lock().await = health_request_bean_clone.pluginVersion;
        *self.productType.lock().await = health_request_bean_clone.productType;
        *self.ideVersion.lock().await = health_request_bean_clone.ideVersion;
        *self.agentVersion.lock().await = health_request_bean_clone.agentVersion;

        let mut repo_git_info_container = self.repoGitInfoContainer.lock().await;
        repo_git_info_container.clear();
        for request_repo_info in health_request_bean_clone.repoGitInfo.unwrap_or(vec![]) {
            let request_repo_info_clone = request_repo_info.clone();
            if request_repo_info.projectUrl.is_none() {
                continue;
            }
            let project_url = request_repo_info.projectUrl.unwrap();
            if project_url.is_empty() {
                continue;
            }
            repo_git_info_container.insert(project_url, request_repo_info_clone);
        }
    }

    pub async fn get_repo_git_info_list (&self) -> Vec<RepoGitInfo> {
        // let container = self.repoGitInfoContainer.lock().await;
        // container.values().cloned().collect()

        //mock data for test
        let mut repo_git_info_list = Vec::new();
        let repo_git_info = RepoGitInfo{
            projectUrl: Some("/Users/<USER>/Downloads/alispace/Qprobe".to_string()),
            gitAddress: None,
            branch: None,
        };
        repo_git_info_list.push(repo_git_info);
        repo_git_info_list
    }

    pub async fn get_user_id(&self) -> Option<String>{
        // let userId_opt = self.userId.lock().await;
        // userId_opt.clone()
        //mock
        Some("078624".to_string())
    }

}

//定时要轮询的任务
pub async fn tick_syn_task() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let merkle_tree_config_opt = get_remote_index_config().await;
    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    info!("request merkle_tree_config_opt  {:?}, cost {}", merkle_tree_config_opt, end_time - start_time);
    match merkle_tree_config_opt {
        None => {return Ok(());}
        Some(merkle_tree_config_v) => {
            if !merkle_tree_config_v.use_remote_index {
                info!("stop all merkle tree task since use_remote_index is false");
                return Ok(());
            }

            let repo_list = REMOTE_DATA_SYN_MANAGER.get_repo_git_info_list().await;
            //遍历仓库列表
            for repo in &repo_list {
                let user_id = REMOTE_DATA_SYN_MANAGER.get_user_id().await.unwrap_or("".to_string());
                if user_id.is_empty() {
                    info!("do nothing since userId is empty");
                    return Ok(());
                }
                let repo_clone = repo.clone();
                let project_url = repo_clone.projectUrl.unwrap_or("".to_string());
                if project_url.is_empty() {
                    info!("tick_syn_task do nothing since project_url is empty");
                    return Ok(());
                }
                let session_id = generate_session_id(&user_id, &project_url);
                let mut request_merkle_tree_data = RequestMerkleTreeData::default();
                request_merkle_tree_data.userId = user_id.clone();
                request_merkle_tree_data.sessionId = session_id.clone();
                //请求remote merkle tree
                let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                let request_merkle_tree_res_opt = request_remote_merkle_tree(&request_merkle_tree_data).await;
                let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
                info!("request request_merkle_tree_res_opt  {:?}, cost {}", request_merkle_tree_res_opt, end_time - start_time);
                if request_merkle_tree_res_opt.is_none() {
                    info!("continue since request_merkle_tree_res_opt is none, user_id {} project_url is {}", user_id, project_url);
                    continue;
                }
                let mut request_merkle_tree_res = request_merkle_tree_res_opt.unwrap();
                let batch_size = request_merkle_tree_res.batchSize;
                let interval_time = request_merkle_tree_res.interval;
                let remote_tree = request_merkle_tree_res.merkleTree.unwrap_or(json!({}));
                info!("remote_tree {}", remote_tree.to_string());
                //生成本地merkle tree
                // let local_tree = build_merkle_tree(&project_url, merkle_tree_config_v.sleep_time).await.unwrap_or(json!({}));
                // let local_tree_rst = build_merkle_tree(&project_url, merkle_tree_config_v.sleep_time).await;
                let local_tree_rst = run_task_with_timeout(build_merkle_tree(&project_url, merkle_tree_config_v.sleep_time), Duration::from_millis(5*1000)).await;
                match local_tree_rst {
                    Ok(v) => {
                        let local_tree = v.0;
                        let build_local_tree_cost_time = v.1;
                        info!("local_tree {}", local_tree.to_string());
                        //比对
                        let compare_rst = compare_merkle_tree(remote_tree, local_tree);
                        info!("compare_rst {:?}", compare_rst);
                        let diff_file = match compare_rst {
                            Ok(result) => result,
                            Err(e) => {
                                info!("compare_merkle_tree fail, user_id {} project_url is {}, error: {}", user_id, project_url, e);
                                continue;
                            }
                        };
                        //上传数据
                        upload_diff_file(&diff_file, &project_url.clone(), batch_size, interval_time, &user_id.clone(), &session_id.clone()).await;
                    }
                    Err(e) => {
                        info!("build_merkle_tree timeout or error {:?}", e);
                    }
                }
            }
        }
    }
    Ok(())
}

async fn upload_diff_file(diff_result: &DiffResult, project_url: &String, batch_size: usize, interval_time: usize, user_id: &String, session_id: &String) {
    info!("upload_diff_file, diff_result: {:?}", diff_result);
    let diff_file_vec = get_diff_file_vec(diff_result);
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    for chunk in diff_file_vec.chunks(batch_size) {
        let mut diff_file_to_upload = Vec::new();
        for item in chunk {
            //读取文件内容
            let mut item_clone = item.clone();
            let content = read_file_smart_sync(&Path::new(project_url).join(&item.relativePath).to_string_lossy().to_string()).unwrap_or("".to_string());
            if content.is_empty(){
                continue;
            }
            item_clone.content = content;
            diff_file_to_upload.push(item_clone);
        }
        info!("diff_file_to_upload {}", serde_json::to_string(&diff_file_to_upload).unwrap());

        let mut upload_diff_file_data_wrapper = UploadDiffFileData {
            userId: user_id.clone(),
            sessionId: session_id.clone(),
            repoUrl: None,
            branch: None,
            commit: None,
            data: DiffFileDataWrapper { embeddingModel: "".to_string(), files: diff_file_to_upload },
        };

        let upload_diff_file_res = request_upload_diff_file(&upload_diff_file_data_wrapper).await;
        info!("upload_diff_file_res {:?}", upload_diff_file_res);

        //按要求在上传间隔进行sleep,
        sleep(Duration::from_millis(interval_time as u64)).await;
    }
    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    info!("upload_diff_file cost {}" , end_time - start_time);
}

pub fn start_remote_data_sync_task() {
    //保证只启动一次
    if INIT_TICK_TASK.compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst).is_ok() {
        tokio::spawn(async move {
            loop {
                if let Err(e) = tick_syn_task().await {
                    error!("Sync task failed: {}", e);
                }
                tokio::time::sleep(Duration::from_secs(TICK_INTERVAL_SECS_TIME as u64)).await;
            }
        });

    }
}
//实时remote index检索
pub async fn real_time_search_from_remote(project_url: &String, query: &String) -> Option<Vec<ChatRelatedCodeModel>> {
    info!("real_time_search_from_remote project_url {}, query {}", project_url, query);
    if let Some(remote_index_config) = get_remote_index_config().await {
        if remote_index_config.use_remote_index {
            //本地
            let local_merkle_tree_rst = build_merkle_tree(project_url, remote_index_config.sleep_time).await;
            let local_merkle_tree = match local_merkle_tree_rst {
                Ok(tree) => {
                    let local_tree = tree.0;
                    let build_local_tree_cost = tree.1;
                    
                },
                Err(e) => {
                    info!("build_merkle_tree fail, project_url is {}, error: {}", project_url, e);
                    return None;
                }
            };
            //remote
            let mut request_merkle_tree_data = RequestMerkleTreeData::default();
            let user_id = REMOTE_DATA_SYN_MANAGER.get_user_id().await.unwrap_or("".to_string());
            info!("user_id {}", user_id);
            if user_id.is_empty() {
                info!("return real_time_search_from_remote since user_id is empty");
                return None;
            }
            let session_id = generate_session_id(&user_id, project_url);
            request_merkle_tree_data.userId = user_id.clone();
            request_merkle_tree_data.sessionId = session_id.clone();
            //请求remote merkle tree
            let request_merkle_tree_res_opt = request_remote_merkle_tree(&request_merkle_tree_data).await;
            if request_merkle_tree_res_opt.is_none() {
                info!("continue since real_time_search_from_remote is none, user_id {} project_url is {}", user_id, project_url);
                return None;
            }
            let mut request_merkle_tree_res = request_merkle_tree_res_opt.unwrap();
            info!("request_merkle_tree_res {}", serde_json::to_string_pretty(&request_merkle_tree_res).unwrap());
            let batch_size = request_merkle_tree_res.batchSize;
            let interval_time = request_merkle_tree_res.interval;
            // let remote_tree = serde_json::from_str(&request_merkle_tree_res.merkleTree.unwrap_or("".to_string())).unwrap_or(json!({}));
            let remote_tree = request_merkle_tree_res.merkleTree.unwrap_or(json!({}));
            //比对
            let compare_rst = compare_merkle_tree(remote_tree, local_merkle_tree);
            let diff_file = match compare_rst {
                Ok(result) => result,
                Err(e) => {
                    info!("compare_merkle_tree fail, user_id {} project_url is {}, error: {}", user_id, project_url, e);
                    return None;
                }
            };
            //数据量比较大，以及耗时太长怎么处理 ???
            let mut diff_file_vec = get_diff_file_vec(&diff_file);

            for diff_item in &mut diff_file_vec {
                let content = read_file_smart_sync(&Path::new(project_url).join(&diff_item.relativePath).to_string_lossy().to_string()).unwrap_or("".to_string());
                diff_item.content = content;
            }

            let local_diff_data = LocalDiffDataForSearchFromRemote {
                embeddingModel: None,
                // rerankModel: Some("Codefuse_Reranker".to_string()),
                rerankModel: None,
                // files: Some(diff_file_vec),
                files: None,
            };

            let mut search_from_remote_request_data = SearchFromRemoteRequestData::default();
            search_from_remote_request_data.userId = user_id.clone();
            search_from_remote_request_data.sessionId = session_id;
            search_from_remote_request_data.query = query.to_string();
            search_from_remote_request_data.threshold = Some(0.0);
            search_from_remote_request_data.limit = Some(10);
            search_from_remote_request_data.data = Some(local_diff_data);

            let remote_rag_data_opt = request_search_from_remote(&search_from_remote_request_data).await;
            if remote_rag_data_opt.is_none() {
                info!("continue since remote_rag_data_opt is none, user_id {} project_url is {}", user_id, project_url);
                return None;
            }
            let remote_rag_data = remote_rag_data_opt.unwrap();
            info!("remote_rag_data {:?}", remote_rag_data);
            return Some(remote_rag_data.snippets);
        }
    }
    None
}

#[cfg(test)]
mod tests {
    use std::env::set_var;
    use std::error::Error;
    use std::time::{Duration, SystemTime, UNIX_EPOCH};
    use agent_db::config::agent_logger::init_logger;
    use agent_db::config::runtime_config::AGENT_CONFIG_ENV_KEY;
    use log::info;
    use serde_json::Value;
    use crate::remote::merkle_tree::build_merkle_tree;
    use crate::remote::remote_syn_data::{MerkleTreeNode, RequestMerkleTreeData, UpdateMerkleTreeData};
    use crate::remote::remote_syn_net::{request_merkle_tree_config, request_remote_merkle_tree, request_update_merkle_tree};
    use crate::remote::remote_syn_task::{real_time_search_from_remote, tick_syn_task};
    use crate::remote::remote_util::generate_session_id;
    use crate::remote::timeout_fun::run_task_with_timeout;

    #[test]
    fn test_timeout() {
        unsafe { set_var(AGENT_CONFIG_ENV_KEY, "--code-complation-window-size 20"); }
        let _ = init_logger();
        let project_url = "/Users/<USER>/Downloads/alispace/caselike".to_string();

        let runtime = tokio::runtime::Runtime::new().unwrap();
        let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        let rst = runtime.block_on(run_task_with_timeout(
            async move {
                build_merkle_tree(&project_url, 0).await.map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))
            },
            Duration::from_millis(5000)
        ));
        let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
        info!("cost {}", end_time - start_time);
        match rst {
            Ok(v) => {
                println!("test_timeout tree_rst {:?}", v)
            }
            Err(e) => {
                println!("error {:?}", e);
            }
        }

    }

    #[test]
    fn test_request_tree() {
        unsafe { set_var(AGENT_CONFIG_ENV_KEY, "--code-complation-window-size 20"); }
        let _ = init_logger();
        let mut request_merkle_tree_data = RequestMerkleTreeData::default();
        //let user_id = REMOTE_DATA_SYN_MANAGER.get_user_id().await.unwrap_or("".to_string());
        // info!("user_id {}", user_id);
        // if user_id.is_empty() {
        //     info!("return real_time_search_from_remote since user_id is empty");
        //     return None;
        // }
        let user_id = "078624".to_string();
        let project_url = "/Users/<USER>/Downloads/alispace/Qprobe".to_string();
        let session_id = generate_session_id(&user_id, &project_url);
        request_merkle_tree_data.userId = user_id.clone();
        request_merkle_tree_data.sessionId = session_id.clone();
        println!("rst : {:?}", serde_json::to_string_pretty(&request_merkle_tree_data).unwrap());

        let runtime = tokio::runtime::Runtime::new().unwrap();
        let rst = runtime.block_on(request_remote_merkle_tree(&request_merkle_tree_data));
        println!("rst : {:?}", rst)
    }

    #[test]
    fn test_request_upload() {
        unsafe { set_var(AGENT_CONFIG_ENV_KEY, "--code-complation-window-size 20"); }
        let _ = init_logger();
        info!("test_request_upload");
        let runtime = tokio::runtime::Runtime::new().unwrap();
        let rst = runtime.block_on(tick_syn_task());
        // let rst = runtime.block_on(request_merkle_tree_config());
        println!("rst : {:?}", rst)
    }

    #[test]
    fn test_real_time_search() {
        unsafe { set_var(AGENT_CONFIG_ENV_KEY, "--code-complation-window-size 20"); }
        let _ = init_logger();
        info!("test_real_time_search");
        let runtime = tokio::runtime::Runtime::new().unwrap();
        let project_url = "/Users/<USER>/Downloads/alispace/Qprobe".to_string();
        // let query = "仓库的核心功能是啥？".to_string();
        let query = "calculateLocalFileHash的功能是啥？".to_string();
        let rst = runtime.block_on(real_time_search_from_remote(&project_url, &query));
        // let rst = runtime.block_on(request_merkle_tree_config());
        println!("rst : {:?}", rst)
    }

    #[test]
    fn test_update_merkle_tree() {
        unsafe { set_var(AGENT_CONFIG_ENV_KEY, "--code-complation-window-size 20"); }
        let _ = init_logger();

        let user_id = "078624".to_string();
        let project_url = "/Users/<USER>/Downloads/alispace/Qprobe".to_string();

        let runtime = tokio::runtime::Runtime::new().unwrap();
        let merkle_tree_value = runtime.block_on(build_merkle_tree(&project_url.clone(), 0));

        if merkle_tree_value.is_ok() {
            let merkle_tree = merkle_tree_value.unwrap();
            let merkle_tree_clone = merkle_tree.clone();
            let merkle_tree_str = merkle_tree_clone.to_string();
            println!("merkle_tree_str {}", merkle_tree_str);

            let root_hash = merkle_tree.get("__tree_hash__").unwrap().as_str().unwrap().to_string();
            println!("root_hash {}", root_hash);
            let merkle_tree_node = MerkleTreeNode {
                rootHash: root_hash,
                merkleTree: merkle_tree_str,
            };

            let update_merkle_tree = UpdateMerkleTreeData {
                userId: user_id,
                sessionId: project_url,
                repoUrl: None,
                branch: None,
                commit: None,
                data: merkle_tree_node,
            };

            let update_merkle_tree_res = runtime.block_on(request_update_merkle_tree(&update_merkle_tree));
            info!("update_merkle_tree_res {:?}", update_merkle_tree_res);

            let mut request_merkle_tree_data = RequestMerkleTreeData::default();

            let user_id = "078624".to_string();
            let project_url = "/Users/<USER>/Downloads/alispace/Qprobe".to_string();
            let session_id = generate_session_id(&user_id, &project_url);
            request_merkle_tree_data.userId = user_id.clone();
            request_merkle_tree_data.sessionId = session_id.clone();
            println!("rst : {:?}", serde_json::to_string_pretty(&request_merkle_tree_data).unwrap());

            let runtime = tokio::runtime::Runtime::new().unwrap();
            let rst = runtime.block_on(request_remote_merkle_tree(&request_merkle_tree_data));
            println!("rst : {:?}", rst)

        }
    }
}