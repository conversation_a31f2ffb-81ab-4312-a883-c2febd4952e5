use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashSet;
use std::time::{SystemTime, UNIX_EPOCH};
use log::info;

#[derive(Debug, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct DiffResult {
    #[serde(default)]
    pub added_files: Vec<String>,
    #[serde(default)]
    pub removed_files: Vec<String>,
    #[serde(default)]
    pub modified_files: Vec<String>,
    #[serde(default)]
    pub added_dirs: Vec<String>,
    #[serde(default)]
    pub removed_dirs: Vec<String>,
    #[serde(default)]
    pub modified_dirs: Vec<String>,
}

impl DiffResult {
    pub fn new() -> Self {
        Self {
            added_files: Vec::new(),
            removed_files: Vec::new(),
            modified_files: Vec::new(),
            added_dirs: Vec::new(),
            removed_dirs: Vec::new(),
            modified_dirs: Vec::new(),
        }
    }

    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string_pretty(self)
    }
}

use serde_json::Value as JsonValue;

pub struct MerkleTreeComparator {
    tree1: JsonValue,
    tree2: JsonValue,
    diff_result: DiffResult,
}

impl MerkleTreeComparator {
    pub fn new(tree1: JsonValue, tree2: JsonValue) -> Self {
        Self {
            tree1,
            tree2,
            diff_result: DiffResult::new(),
        }
    }

    fn is_file_node(&self, node: &JsonValue) -> bool {
        if let Some(s) = node.as_str() {
            s.contains(':')
        } else {
            false
        }
    }

    fn get_node_hash(&self, node: &JsonValue) -> String {
        if let Some(s) = node.as_str() {
            if s.contains(':') {
                return s.splitn(2, ':').nth(1).unwrap_or("").to_string();
            }
        } else if let Some(obj) = node.as_object() {
            if let Some(tree_hash) = obj.get("__tree_hash__").and_then(|v| v.as_str()) {
                return tree_hash.splitn(2, ':').nth(1).unwrap_or("").to_string();
            }
        }
        String::new()
    }

    fn compare_trees_recursive(
        &mut self,
        tree1: &JsonValue,
        tree2: &JsonValue,
        current_path: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let obj1 = tree1.as_object().ok_or("tree1 is not an object")?;
        let obj2 = tree2.as_object().ok_or("tree2 is not an object")?;

        // 处理根目录hash
        if obj1.contains_key("__tree_hash__") && obj2.contains_key("__tree_hash__") {
            let hash1 = self.get_node_hash(&obj1["__tree_hash__"]);
            let hash2 = self.get_node_hash(&obj2["__tree_hash__"]);
            if hash1 != hash2 {
                self.diff_result
                    .modified_dirs
                    .push(current_path.to_string());
            }
        }

        let keys1: HashSet<_> = obj1.keys().filter(|&k| k != "__tree_hash__").collect();
        let keys2: HashSet<_> = obj2.keys().filter(|&k| k != "__tree_hash__").collect();

        let added: Vec<_> = keys2.difference(&keys1).cloned().collect();
        let removed: Vec<_> = keys1.difference(&keys2).cloned().collect();
        let common: Vec<_> = keys1.intersection(&keys2).cloned().collect();

        // 处理新增的文件和目录
        for key in added {
                let new_path = self.join_path(current_path, key);
            let node = &obj2[key];

            if self.is_file_node(node) {
                self.diff_result.added_files.push(new_path);
            } else {
                self.diff_result.added_dirs.push(new_path.clone());
                self.add_all_children(node, &new_path)?;
            }
        }

        // 处理删除的文件和目录
        for key in removed {
                let old_path = self.join_path(current_path, key);
            let node = &obj1[key];

            if self.is_file_node(node) {
                self.diff_result.removed_files.push(old_path);
            } else {
                self.diff_result.removed_dirs.push(old_path);
            }
        }

        // 处理共同存在的文件和目录
        for key in common {
            let path = self.join_path(current_path, key);
            let node1 = &obj1[key];
            let node2 = &obj2[key];

            if self.is_file_node(node1) && self.is_file_node(node2) {
                if self.get_node_hash(node1) != self.get_node_hash(node2) {
                    self.diff_result.modified_files.push(path);
                }
            } else if !self.is_file_node(node1) && !self.is_file_node(node2) {
                self.compare_trees_recursive(node1, node2, &path)?;
            } else {
                // 类型不匹配（一个是文件，一个是目录）
                if self.is_file_node(node1) {
                    self.diff_result.removed_files.push(path.clone());
                    self.diff_result.added_dirs.push(path);
                } else {
                    self.diff_result.removed_dirs.push(path.clone());
                    self.diff_result.added_files.push(path);
                }
            }
        }

        Ok(())
    }

    fn add_all_children(
        &mut self,
        tree: &JsonValue,
        current_path: &str,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let obj = tree.as_object().ok_or("tree is not an object")?;

        for (key, node) in obj {
            if key == "__tree_hash__" {
                continue;
            }

            let new_path = self.join_path(current_path, key);

            if self.is_file_node(node) {
                self.diff_result.added_files.push(new_path);
            } else {
                self.diff_result.added_dirs.push(new_path.clone());
                self.add_all_children(node, &new_path)?;
            }
        }

        Ok(())
    }

    fn join_path(&self, current_path: &str, key: &str) -> String {
        if current_path.is_empty() {
            key.to_string()
        } else {
            format!("{}/{}", current_path, key).replace("//", "/")
        }
    }

    pub fn compare(&mut self) -> Result<&DiffResult, Box<dyn std::error::Error + Send + Sync>> {
        let tree1 = self.tree1.clone();
        let tree2 = self.tree2.clone();
        self.compare_trees_recursive(&tree1, &tree2, "")?;
        Ok(&self.diff_result)
    }
}

//对两个merkle tree做diff
pub fn compare_merkle_tree(value_1: JsonValue, value_2: JsonValue) -> Result<DiffResult, Box<dyn std::error::Error + Send + Sync>> {
    let start_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    let mut comparator = MerkleTreeComparator::new(value_1, value_2);
    let result = comparator.compare()?;
    let end_time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis();
    info!("compare_merkle_tree cost {}", end_time - start_time);
    Ok(result.clone())
}

pub fn test_merkle_tree_diff() -> Result<(), Box<dyn std::error::Error + Send + Sync>>{
    let tree1 = json!({
        "__tree_hash__": "040000:d3a54a85b7a608ce1b67905044981fd512435b66",
        ".gitignore": "100644:e8e92845b2e86786eaad2a430a87bc1e3ead02e5",
        "copy_scc.yml": "100644:fd6cc8d44600834e08a83771767aa4a6c95cbf1e",
        "download_and_install_git.yml": "100644:a7889f039e3701e1215d1b7cc9cf20ec4ebbdace",
        "download_and_install_pinpoint.yml": "100644:1a2738c659b389149cd129f85a24332cc86ab045",
        "exec.yml": "100644:fd32734243ca95511a24713969f2fc6fbaad57cc",
        "extract_pinpoint.yml": "100644:f259f7a062e32b927f4f094ff27b0ebb2ed07a25",
        "init.yml": "100644:d4c5d5a96f34fa31af9c7accb059c47b70d1f6ae",
        "messages.json": "100644:720bfc9a55a317903d0c7ac9c8131fecea7d278c",
        "pip_install.yml": "100644:22a7539ff2a9e7660aef0c4ade876d599dc62eb5",
        "scc": "100755:adc9e056110d0e5ad6b03a795dc61492e4d80103",
        "scp_files.yaml": "100644:f71b9e565926383e8c9128ce3aa9d61423a5e3ba",
        "start.yml": "100644:7e0e9d70f45fba42d024576921a573e0bcfa87e2",
        "stop.yml": "100644:cec70ae28b1f6c719547c23b68795b312ce9efa6",
        "sync.yml": "100644:e00a81441d98a2af804246eed7753aca26f66855",
        "update_supervisor_conf": {
            "__tree_hash__": "040000:3decb0e735808046ce2c7c59b1850f4c84450992",
            "antcode_pr_check": {
                "__tree_hash__": "040000:dd406b99fd7bb243c605015e13835439bb83cf82",
                "beat.yml": "100644:6726832f821c53a8ab5fc056c2ef971672d44a94",
                "distribution_worker.yml": "100644:a52ee42b6d162a01e10685c1983bca740feb29bf",
                "pr_built_worker.yml": "100644:4ff6069f6e4abcbbab4afa5b62dcbf81e508c461",
                "support.yml": "100644:4cb4ad428fed26e68dbd3c5acfb32760ace6e00c",
                "task_center_worker.yml": "100644:667b7047acefe5d4280fde5e5d4b673b5ab1d444",
                "test_worker.yml": "100644:b9f488cdf3d1e86217a8242806a7b65b3d26400b",
                "worker.yml": "100644:4bc322e98dddc22a4768bd159f4eb50abf4e4683"
            },
            "fund_loss": {
                "__tree_hash__": "040000:6f7ec866edc2a6b1af750fd69484e9a2cad8111c",
                "gateway.yml": "100644:484cc39a5c5a73d4075e70c1da35d28b0ec40ca3",
                "gateway_and_beat.yml": "100644:2e7731d40760656b2355ac13a851eb3091bc170b",
                "test_gateway.yml": "100644:3a131318356cf350d1a3e47aed75669d562fb412",
                "test_worker.yml": "100644:4f92a2c3140ade6db821a8b1282ae77ec78dbf0c",
                "worker.yml": "100644:f4d1c129336d86a530a9a690828ccb121df2f932",
                "worker_and_support.yml": "100644:df6debad4bbf3023e92181f1f69328e5ab4e2953"
            },
            "pipeline_scan": {
                "__tree_hash__": "040000:b92a19b68d2047ba7d0d6b54108ea5fa6c480110",
                "warehousing.yml": "100644:3318c299329387fa90c7fd551ec3a71a3eca9241"
            }
        },
        "upload.yml": "100644:df7b5c54c31e61f20a1fe838b0e298ea68acd2de"
    });

    let tree2 = json!({
        "__tree_hash__": "040000:64c124e9c33286cc1a1060466000a49d2002bc8b",
        ".gitignore": "100644:e8e92845b2e86786eaad2a430a87bc1e3ead02e5",
        "copy_scc.yml": "100644:fd6cc8d44600834e08a83771767aa4a6c95cbf1e",
        "download_and_install_git.yml": "100644:a7889f039e3701e1215d1b7cc9cf20ec4ebbdace",
        "download_and_install_pinpoint.yml": "100644:1a2738c659b389149cd129f85a24332cc86ab045",
        "exec.yml": "100644:fd32734243ca95511a24713969f2fc6fbaad57cc",
        "extract_pinpoint.yml": "100644:f259f7a062e32b927f4f094ff27b0ebb2ed07a25",
        "init.yml": "100644:d4c5d5a96f34fa31af9c7accb059c47b70d1f6ae",
        "messages.json": "100644:720bfc9a55a317903d0c7ac9c8131fecea7d278c",
        "pip_install.yml": "100644:22a7539ff2a9e7660aef0c4ade876d599dc62eb5",
        "scc": "100755:adc9e056110d0e5ad6b03a795dc61492e4d80103",
        "scp_files.yaml": "100644:f71b9e565926383e8c9128ce3aa9d61423a5e3ba",
        "start.yml": "100644:7e0e9d70f45fba42d024576921a573e0bcfa87e2",
        "stop.yml": "100644:cec70ae28b1f6c719547c23b68795b312ce9efa6",
        "sync.yml": "100644:e00a81441d98a2af804246eed7753aca26f66855",
        "update_supervisor_conf": {
            "__tree_hash__": "040000:433ea117fa84706683413c618e833d83f862ceaf",
            "antcode_pr_check": {
                "__tree_hash__": "040000:b1cdd63efd4d512a7a39a47fb0791ed071446ccf",
                "beat.yml": "100644:6726832f821c53a8ab5fc056c2ef971672d44a94",
                "distribution_worker.yml": "100644:a52ee42b6d162a01e10685c1983bca740feb29bf",
                "pr_built_worker.yml": "100644:4ff6069f6e4abcbbab4afa5b62dcbf81e508c461",
                "support.yml": "100644:6385107e9857f8e151f2db8d1420b92a66071720",
                "task_center_worker.yml": "100644:667b7047acefe5d4280fde5e5d4b673b5ab1d444",
                "test_worker.yml": "100644:b9f488cdf3d1e86217a8242806a7b65b3d26400b",
                "worker.yml": "100644:4bc322e98dddc22a4768bd159f4eb50abf4e4683"
            },
            "fund_loss": {
                "__tree_hash__": "040000:2aa82cedfcb4e7e74852737ceea082e3f3cf2a3a",
                "gateway.yml": "100644:8b75d15151b61fd25e25f55e7561d3ec1aaf6b24",
                "gateway_and_beat.yml": "100644:2e7731d40760656b2355ac13a851eb3091bc170b",
                "test_gateway.yml": "100644:3a131318356cf350d1a3e47aed75669d562fb412",
                "test_worker.yml": "100644:4f92a2c3140ade6db821a8b1282ae77ec78dbf0c",
                "worker.yml": "100644:f4d1c129336d86a530a9a690828ccb121df2f932",
                "worker_and_support.yml": "100644:df6debad4bbf3023e92181f1f69328e5ab4e2953"
            },
            "pipeline_scan": {
                "__tree_hash__": "040000:b92a19b68d2047ba7d0d6b54108ea5fa6c480110",
                "warehousing.yml": "100644:3318c299329387fa90c7fd551ec3a71a3eca9241"
            }
        },
        "upload.yml": "100644:df7b5c54c31e61f20a1fe838b0e298ea68acd2de"
    });

    let mut comparator = MerkleTreeComparator::new(tree1, tree2);
    let result = comparator.compare()?;

    println!("Comparison result:");
    println!("{}", result.to_json().unwrap());
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_merkle_tree_comparator() {
        let tree1 = json!({
            "__tree_hash__": "040000:d3a54a85b7a608ce1b67905044981fd512435b66",
            ".gitignore": "100644:e8e92845b2e86786eaad2a430a87bc1e3ead02e5",
            "file1.txt": "100644:abc123",
            "dir1": {
                "__tree_hash__": "040000:hash1",
                "nested_file.txt": "100644:def456"
            }
        });

        let tree2 = json!({
            "__tree_hash__": "040000:64c124e9c33286cc1a1060466000a49d2002bc8b",
            ".gitignore": "100644:e8e92845b2e86786eaad2a430a87bc1e3ead02e5",
            "file1.txt": "100644:abc456",  // 修改的文件
            "dir1": {
                "__tree_hash__": "040000:hash2",  // 修改的目录
                "nested_file.txt": "100644:def456",
                "new_file.txt": "100644:ghi789"  // 新增文件
            },
            "dir2": {
                "__tree_hash__": "040000:hash3",
                "another_file.txt": "100644:jkl012"  // 新增目录和文件
            }
        });

        let mut comparator = MerkleTreeComparator::new(tree1, tree2);
        let result = comparator.compare().unwrap();

        assert!(result.added_files.contains(&"dir1/new_file.txt".to_string()));
        assert!(result.added_files.contains(&"dir2/another_file.txt".to_string()));
        assert!(result.added_dirs.contains(&"dir2".to_string()));
        assert!(result.modified_files.contains(&"file1.txt".to_string()));
    }
}

// 主函数示例，类似于 Python 的 __main__
#[cfg(not(test))]
fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 使用示例
    let tree1 = json!({
        "__tree_hash__": "040000:hash1",
        "file1.txt": "100644:abc123",
        "dir1": {
            "__tree_hash__": "040000:dirhash1",
            "nested_file.txt": "100644:def456"
        }
    });

    let tree2 = json!({
        "__tree_hash__": "040000:hash2",
        "file1.txt": "100644:abc456",  // 修改的文件
        "dir1": {
            "__tree_hash__": "040000:dirhash2",  // 修改的目录
            "nested_file.txt": "100644:def456",
            "new_file.txt": "100644:ghi789"  // 新增文件
        },
        "dir2": {
            "__tree_hash__": "040000:dirhash3",
            "another_file.txt": "100644:jkl012"  // 新增目录和文件
        }
    });

    let mut comparator = MerkleTreeComparator::new(tree1, tree2);
    let result = comparator.compare()?;
    
    println!("Comparison result:");
    println!("{}", result.to_json().unwrap());
    
    Ok(())
}